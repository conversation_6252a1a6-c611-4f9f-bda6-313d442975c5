# Glory Real Estate Company Limited Website

A professional real estate website with modular header and footer components for easy maintenance and consistency across all pages.

## Project Structure

### Components
- `components/header.html` - Reusable header component with navigation
- `components/footer.html` - Reusable footer component with company information
- `js/include.js` - JavaScript for loading components and handling interactions

### Pages
- `index1.html` - Home page (main landing page)
- `properties.html` - Featured properties listing
- `popot.html` - All properties page
- `property-details.html` - Detailed property view
- `formregister.html` - User registration form
- `login.html` - User login form
- `contact.html` - Contact form and company information
- `services.html` - Company services overview
- `agents.html` - Meet our agents page
- `about.html` - About the company
- `terms.html` - Terms and conditions
- `forgot-password.html` - Password reset form

### Styles
- `style.css` - Main stylesheet with responsive design
- `style2.css` - Additional styles for forms
- `style3.css` - Login/authentication page styles
- `css/properties.css` - Property-specific styles
- `css/property-details.css` - Property details page styles

### Assets
- `picture/` - Property images and icons
- `p1.jpeg` to `p5.jpeg` - Property showcase images

## Features

### Modular Design
- Header and footer components are loaded dynamically
- Consistent navigation across all pages
- Easy to maintain and update

### Responsive Layout
- Mobile-friendly design
- Hamburger menu for mobile devices
- Flexible grid layouts

### Professional Forms
- User registration with validation
- Contact forms with multiple subjects
- Property viewing request forms

### Navigation
- Active page highlighting
- Breadcrumb navigation on detail pages
- Social media links in footer

## How to Use

### Adding New Pages
1. Create a new HTML file
2. Add the header and footer placeholders:
   ```html
   <div id="header-placeholder"></div>
   <!-- Your page content here -->
   <div id="footer-placeholder"></div>
   ```
3. Include the JavaScript file:
   ```html
   <script src="js/include.js"></script>
   ```

### Updating Header/Footer
- Edit `components/header.html` for navigation changes
- Edit `components/footer.html` for footer content changes
- Changes will automatically apply to all pages

### Customizing Styles
- Main styles are in `style.css`
- Page-specific styles can be added to separate CSS files
- Use CSS variables defined in `:root` for consistent theming

## Browser Compatibility
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers
- Responsive design for tablets and phones

## Contact Information
- Company: Glory Real Estate Company Limited
- Location: Mikocheni, Dar es Salaam, Tanzania
- Phone: +*********** 789
- Email: <EMAIL>

## Development Notes
- All pages use the same header/footer components
- JavaScript handles dynamic content loading
- CSS uses modern flexbox and grid layouts
- Images are optimized for web use
- Forms include basic validation
