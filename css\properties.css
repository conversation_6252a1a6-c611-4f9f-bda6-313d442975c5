/* Properties Page Styles */

.page-header {
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1773&q=80');
    background-size: cover;
    background-position: center;
    padding: 120px 0 60px;
    color: white;
    text-align: center;
    position: relative;
}

.page-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 70px;
    background: linear-gradient(to top, rgba(245, 245, 245, 1), rgba(245, 245, 245, 0));
}

.page-header h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    animation: fadeInDown 1s ease;
}

.breadcrumb {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    font-size: 1rem;
    margin-top: 1rem;
    animation: fadeIn 1s ease 0.3s forwards;
    opacity: 0;
}

.breadcrumb a {
    color: var(--secondary-color);
    transition: var(--transition);
}

.breadcrumb a:hover {
    color: white;
    text-decoration: underline;
}

.breadcrumb span {
    opacity: 0.8;
}

.properties-section {
    padding: 5rem 0;
    background-color: #f5f5f5;
}

.properties-filter {
    background-color: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    margin-bottom: 3rem;
    animation: fadeInUp 1s ease;
}

.filter-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.filter-group select,
.filter-group input {
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.95rem;
    transition: var(--transition);
}

.filter-group select:focus,
.filter-group input:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.2);
    outline: none;
}

.price-range {
    display: flex;
    gap: 1rem;
}

.price-range input {
    flex: 1;
}

.advanced-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.filter-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: var(--secondary-color);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 1.5rem;
}

.filter-toggle:hover {
    color: #c0392b;
}

.filter-toggle i {
    transition: var(--transition);
}

.filter-toggle.active i {
    transform: rotate(180deg);
}

.filter-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.5rem;
}

.filter-btn {
    padding: 12px 30px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.apply-filter {
    background-color: var(--secondary-color);
    color: white;
    border: none;
    flex: 1;
    max-width: 200px;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.apply-filter:hover {
    background-color: #c0392b;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

.reset-filter {
    background-color: transparent;
    color: var(--gray-color);
    border: 1px solid var(--border-color);
    margin-right: 1rem;
}

.reset-filter:hover {
    background-color: #f8f9fa;
    color: var(--primary-color);
}

.view-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background-color: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.sort-by {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sort-by label {
    font-weight: 600;
    color: var(--primary-color);
}

.sort-by select {
    padding: 8px 15px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.95rem;
    transition: var(--transition);
}

.sort-by select:focus {
    border-color: var(--secondary-color);
    outline: none;
}

.view-type {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: white;
    color: var(--gray-color);
    cursor: pointer;
    transition: var(--transition);
}

.view-btn:hover {
    color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.view-btn.active {
    background-color: var(--secondary-color);
    color: white;
    border-color: var(--secondary-color);
}

.properties-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2.5rem;
    margin-bottom: 3rem;
}

.property-card {
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: transform 0.4s ease, box-shadow 0.4s ease;
    cursor: pointer;
}

.property-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.property-img {
    height: 250px;
    position: relative;
    overflow: hidden;
}

.property-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.property-card:hover .property-img img {
    transform: scale(1.1);
}

.property-tag {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 8px 15px;
    border-radius: 30px;
    font-weight: 600;
    font-size: 0.8rem;
    z-index: 1;
}

.for-sale {
    background-color: var(--secondary-color);
    color: white;
}

.for-rent {
    background-color: var(--accent-color);
    color: white;
}

.favorite-btn {
    position: absolute;
    top: 15px;
    left: 15px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    z-index: 1;
    opacity: 0;
    transform: translateY(-10px);
}

.property-card:hover .favorite-btn {
    opacity: 1;
    transform: translateY(0);
}

.favorite-btn:hover {
    background-color: var(--secondary-color);
    color: white;
}

.favorite-btn.active {
    background-color: var(--secondary-color);
    color: white;
}

.property-content {
    padding: 1.5rem;
}

.property-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.property-title {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
    transition: var(--transition);
}

.property-card:hover .property-title {
    color: var(--secondary-color);
}

.property-address {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--gray-color);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.property-specs {
    display: flex;
    justify-content: space-between;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.property-specs span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--gray-color);
}

.view-details {
    display: block;
    width: 100%;
    padding: 12px 0;
    margin-top: 1.5rem;
    text-align: center;
    background-color: var(--primary-color);
    color: white;
    border-radius: 8px;
    font-weight: 600;
    transition: var(--transition);
}

.view-details:hover {
    background-color: var(--secondary-color);
}

.properties-list {
    display: none;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 3rem;
}

.list-property-card {
    display: grid;
    grid-template-columns: 300px 1fr;
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: transform 0.4s ease, box-shadow 0.4s ease;
    cursor: pointer;
}

.list-property-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.list-property-img {
    height: 100%;
    position: relative;
    overflow: hidden;
}

.list-property-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.list-property-card:hover .list-property-img img {
    transform: scale(1.1);
}

.list-property-content {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
}

.list-property-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.list-property-title h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
    transition: var(--transition);
}

.list-property-card:hover .list-property-title h3 {
    color: var(--secondary-color);
}

.list-property-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--secondary-color);
    text-align: right;
}

.list-property-address {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--gray-color);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.list-property-description {
    margin-bottom: 1.5rem;
    color: var(--gray-color);
    line-height: 1.6;
}

.list-property-specs {
    display: flex;
    gap: 2rem;
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.list-property-specs span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--gray-color);
}

.list-view-details {
    display: inline-block;
    padding: 12px 30px;
    margin-top: 1.5rem;
    text-align: center;
    background-color: var(--primary-color);
    color: white;
    border-radius: 8px;
    font-weight: 600;
    transition: var(--transition);
    align-self: flex-start;
}

.list-view-details:hover {
    background-color: var(--secondary-color);
}

.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 3rem;
}

.page-link {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: white;
    color: var(--gray-color);
    font-weight: 600;
    transition: var(--transition);
}

.page-link:hover {
    background-color: #f8f9fa;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.page-link.active {
    background-color: var(--secondary-color);
    color: white;
    border-color: var(--secondary-color);
}

.page-link.prev,
.page-link.next {
    width: auto;
    padding: 0 15px;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Styles */
@media (max-width: 992px) {
    .properties-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
    
    .list-property-card {
        grid-template-columns: 250px 1fr;
    }
}

@media (max-width: 768px) {
    .page-header h1 {
        font-size: 2.5rem;
    }
    
    .filter-container {
        grid-template-columns: 1fr;
    }
    
    .view-options {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .sort-by {
        width: 100%;
    }
    
    .sort-by select {
        flex: 1;
    }
    
    .list-property-card {
        grid-template-columns: 1fr;
    }
    
    .list-property-img {
        height: 250px;
    }
}

@media (max-width: 576px) {
    .properties-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-actions {
        flex-direction: column;
        gap: 1rem;
    }
    
    .apply-filter {
        max-width: 100%;
    }
    
    .reset-filter {
        width: 100%;
        margin-right: 0;
        text-align: center;
    }
}