:root{
 --grey: #2c3e50;
 --text-color: #333;
 --secondary-color: #e74c3c;
 --primary-color: #3498db;
 --accent-color: #f1c40f;
 --border-color: #e0e0e0;
 --light-bg: #f8f9fa;
 --transition: all 0.3s ease;
 --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body{
    font-family: "Arial", sans-serif;
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url("p4.jpeg");
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    min-height: 100vh;    
} 
header{
    background-color: rgba(255, 255, 255, 0.95);
    padding: 16px 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1;
    
}
.company-name{
  text-align: center;
  color: var(--grey);
  font-size: 40px;
  margin-bottom: 16px;
  text-transform: uppercase;
  letter-spacing: 2px;
}
nav{
  display: flex;
  justify-content: center;
  padding: 8px;


}
nav ul{
  display: flex;
  list-style: none;
  gap: 32px;
}

nav a{
  text-decoration: none;
  color: var(--text-color);
  font-weight: 600;
  font-size: 16px;
  padding: 8px 16px;
  border-radius: 4px;
  transition: 0.3s;
}
nav a:hover{
  background-color: var(--secondary-color);
  color: white;
}

.icon img{
  width: 30px;
}

/* Hero Section */
.hero-section {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin-top: 0;
}

.hero-content {
  max-width: 800px;
  padding: 0 20px;
}

.welcome-text {
  font-size: 48px;
  margin-bottom: 16px;
  color: white;
  animation: fadeInDown 1s ease;
}

.subtitle {
  font-size: 24px;
  opacity: 0.9;
  color: rgb(231, 231, 121);
  margin-bottom: 32px;
  animation: fadeInDown 1s ease 1s backwards;
}


.hero-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  animation: fadeInUp 1s ease 0.6s forwards;
  opacity: 0;
}

.btn {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 4px;
  font-weight: 600;
  text-decoration: none;
  transition: var(--transition);
  cursor: pointer;
}

.primary-btn {
  background-color: var(--secondary-color);
  color: white;
}

.primary-btn:hover {
  background-color: #c0392b;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
}

.secondary-btn {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.secondary-btn:hover {
  background-color: white;
  color: var(--grey);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(255, 255, 255, 0.4);
}

/* Search Section */
.search-section {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 40px 0;
  margin-top: 0;
}

.search-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.search-container h2 {
  text-align: center;
  color: var(--grey);
  margin-bottom: 24px;
  font-size: 32px;
}

.search-form {
  background-color: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: var(--shadow);
}

.search-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.search-row select {
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 16px;
  color: var(--text-color);
  width: 100%;
}

.search-btn {
  background-color: var(--secondary-color);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  width: 100%;
  font-size: 16px;
}

.search-btn:hover {
  background-color: #c0392b;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
}

/* Property Types Section */
#property-types {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 24px;
  margin-top: 80px;
  padding: 0 20px;
}

.card1, .card2, .card3 {
  background-color: whitesmoke;
  padding: 32px;
  text-align: center;
  color: var(--text-color);
  border-radius: 8px;
  box-shadow: 0 4px 10px rgb(231, 231, 121);
  cursor: pointer;
  transition: 0.5s;
  width: 350px;
  position: relative;
  overflow: hidden;
}

.card1:hover, .card2:hover, .card3:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 20px rgba(231, 231, 121, 0.7);
}

.card-icon {
  font-size: 48px;
  color: var(--secondary-color);
  margin-bottom: 24px;
}

.card1 h2, .card2 h2, .card3 h2 {
  margin-bottom: 16px;
  color: var(--grey);
}

.card1 p, .card2 p, .card3 p {
  margin-bottom: 24px;
  color: var(--text-color);
  line-height: 1.6;
}

.card-link {
  display: inline-block;
  padding: 8px 16px;
  background-color: var(--secondary-color);
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 600;
  transition: var(--transition);
}

.card-link:hover {
  background-color: #c0392b;
  transform: translateY(-3px);
}

/* Featured Properties Section */
.featured-section {
  padding: 80px 20px;
  background-color: rgba(255, 255, 255, 0.9);
  margin-top: 80px;
}

.section-title {
  text-align: center;
  color: var(--grey);
  font-size: 36px;
  margin-bottom: 40px;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: var(--secondary-color);
}

.featured-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.property-card {
  background-color: white;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: var(--transition);

}

.property-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.property-img {
  height: 220px;
  position: relative;
}

.property-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.property-tag {
  position: absolute;
  top: 16px;
  right: 16px;
  background-color: var(--secondary-color);
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 14px;
}

.property-content {
  padding: 24px;
}

.property-content h3 {
  font-size: 20px;
  margin-bottom: 8px;
  color: var(--grey);
}

.property-address {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-color);
  font-size: 14px;
  margin-bottom: 16px;
}

.property-price {
  font-size: 24px;
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: 16px;
}

.property-specs {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
}

.property-specs span {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--text-color);
}

.property-link {
  display: block;
  text-align: center;
  padding: 12px;
  background-color: var(--grey);
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 600;
  transition: var(--transition);
}

.property-link:hover {
  background-color: var(--secondary-color);
}

.view-all-container {
  text-align: center;
  margin-top: 40px;
}

.view-all-btn {
  background-color: transparent;
  color: var(--grey);
  border: 2px solid var(--grey);
}

.view-all-btn:hover {
  background-color: var(--grey);
  color: white;
}

/* Services Section */
.services-section {
  padding: 80px 20px;
  margin-top: 0;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.service-card {
  background-color: white;
  padding: 32px;
  border-radius: 8px;
  text-align: center;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.service-icon {
  font-size: 48px;
  color: var(--secondary-color);
  margin-bottom: 24px;
}

.service-card h3 {
  font-size: 20px;
  margin-bottom: 16px;
  color: var(--grey);
}

.service-card p {
  color: var(--text-color);
  line-height: 1.6;
}

/* Agents Section */
.agents-section {
  padding: 80px 20px;
  background-color: rgba(255, 255, 255, 0.9);
  margin-top: 0;
}

.agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.agent-card {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.agent-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.agent-img {
  height: 300px;
}

.agent-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.agent-content {
  padding: 24px;
  text-align: center;
}

.agent-content h3 {
  font-size: 20px;
  margin-bottom: 8px;
  color: var(--grey);
}

.agent-title {
  color: var(--secondary-color);
  font-weight: 600;
  margin-bottom: 16px;
}

.agent-contact {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
  color: var(--text-color);
}

/* Testimonials Section */
.testimonials-section {
  padding: 80px 20px;
  margin-top: 0;
}

.testimonials-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.testimonial-card {
  background-color: white;
  border-radius: 8px;
  padding: 32px;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.testimonial-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.testimonial-content {
  margin-bottom: 24px;
  position: relative;
  padding: 0 16px;
}

.testimonial-content::before {
  content: '\201C';
  font-size: 80px;
  position: absolute;
  top: -40px;
  left: -10px;
  color: rgba(231, 76, 60, 0.2);
  font-family: Georgia, serif;
}

.testimonial-content p {
  color: var(--text-color);
  line-height: 1.6;
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 16px;
}

.testimonial-author img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.testimonial-author h4 {
  color: var(--grey);
  margin-bottom: 4px;
}

.testimonial-author p {
  color: var(--secondary-color);
  font-size: 14px;
}

/* Contact Section */
.contact-section {
  padding: 80px 20px;
  background-color: rgba(255, 255, 255, 0.9);
  margin-top: 0;
}

.contact-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.contact-item i {
  font-size: 24px;
  color: var(--secondary-color);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(231, 76, 60, 0.1);
  border-radius: 50%;
}

.contact-item p {
  color: var(--text-color);
}

.contact-form {
  background-color: white;
  padding: 32px;
  border-radius: 8px;
  box-shadow: var(--shadow);
}

.form-group {
  margin-bottom: 16px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 16px;
  transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: var(--secondary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.2);
}

.submit-btn {
  background-color: var(--secondary-color);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  width: 100%;
  font-size: 16px;
}

.submit-btn:hover {
  background-color: #c0392b;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
}

/* Footer */
footer {
  background-color: var(--grey);
  color: white;
  padding: 60px 20px 20px;
  margin-top: 100px;
width: 150%;
}

.footer-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer-about h3,
.footer-links h3,
.footer-contact h3,
.footer-newsletter h3 {
  font-size: 20px;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}

.footer-about h3::after,
.footer-links h3::after,
.footer-contact h3::after,
.footer-newsletter h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 2px;
  background-color: var(--secondary-color);
}

.footer-about p {
  line-height: 1.6;
  margin-bottom: 20px;
  opacity: 0.8;
}

 .social-links { 
  display: flex;
  gap: 16px;
} 

.social-links a {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  transition: var(--transition);
}

.social-links a:hover {
  background-color: var(--secondary-color);
  transform: translateY(-3px);
}

.footer-links ul {
  list-style: none;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-links a {
  color: white;
  opacity: 0.8;
  text-decoration: none;
  transition: var(--transition);
  display: inline-block;
}

.footer-links a:hover {
  opacity: 1;
  color: var(--secondary-color);
  transform: translateX(5px);
}

.footer-contact .contact-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  gap: 10px;
}

.footer-contact .contact-item i {
  color: var(--secondary-color);
  margin-top: 2px;
  width: 20px;
}

.footer-contact .contact-item p {
  margin: 0;
  opacity: 0.8;
  line-height: 1.4;
}

.footer-newsletter p {
  line-height: 1.6;
  margin-bottom: 20px;
  opacity: 0.8;
}

.footer-newsletter form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.footer-newsletter input {
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: white;
}

.footer-newsletter input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.footer-newsletter input:focus {
  outline: none;
  border-color: var(--secondary-color);
}

.footer-newsletter button {
  background-color: var(--secondary-color);
  color: white;
  border: none;
  padding: 12px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
}

.footer-newsletter button:hover {
  background-color: #c0392b;
}
.footer-bottom {
  text-align: center;
  padding-top: 30px;
  margin-top: 40px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 0.7;
  font-size: 14px;
}

/* Main Content Structure */
.main-content {
  padding-top: 120px; /* Account for fixed header */
  min-height: calc(100vh - 200px);
}

/* Page Titles */
.page-title {
  font-size: 2.5rem;
  color: white;
  text-align: center;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.page-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  margin-bottom: 40px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Navigation Active State */
nav ul li a.active {
  color: var(--secondary-color);
  font-weight: 600;
}

/* Mobile Navigation */
.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 10px;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background-color: var(--grey);
  margin: 3px 0;
  transition: 0.3s;
}

@media (max-width: 768px) {
  .hamburger {
    display: flex;
  }

  nav ul {
    position: fixed;
    top: 100%;
    left: -100%;
    width: 100%;
    height: calc(100vh - 100%);
    background-color: rgba(255, 255, 255, 0.95);
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    transition: left 0.3s ease;
    padding-top: 20px;
  }

  nav ul.active {
    left: 0;
  }

  nav ul li {
    margin: 10px 0;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Styles */
@media (max-width: 992px) {
  .hero-content {
    max-width: 600px;
  }
  
  .welcome-text {
    font-size: 40px;
  }
  
  .subtitle {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .company-name {
    font-size: 32px;
  }
  
  nav ul {
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
  }
  
  .welcome-text {
    font-size: 32px;
  }
  
  .subtitle {
    font-size: 18px;
  }
  
  .hero-buttons {
    flex-direction: column;
    gap: 12px;
  }
  
  .section-title {
    font-size: 28px;
  }
}

@media (max-width: 576px) {
  .company-name {
    font-size: 28px;
  }
  
  nav ul {
    gap: 8px;
  }
  
  nav a {
    font-size: 14px;
    padding: 6px 12px;
  }
  
  .welcome-text {
    font-size: 28px;
  }
  
  .subtitle {
    font-size: 16px;
  }
  
  .card1, .card2, .card3 {
    width: 100%;
  }
  
  .section-title {
    font-size: 24px;
  }
  
  .property-specs {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
}
