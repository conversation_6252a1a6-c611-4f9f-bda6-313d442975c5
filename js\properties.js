// DOM Elements
const propertiesGrid = document.getElementById('propertiesGrid');
const propertiesList = document.getElementById('propertiesList');
const gridViewBtn = document.getElementById('gridView');
const listViewBtn = document.getElementById('listView');
const filterToggle = document.querySelector('.filter-toggle');
const advancedFilters = document.querySelector('.advanced-filters');
const resetFilterBtn = document.querySelector('.reset-filter');
const applyFilterBtn = document.querySelector('.apply-filter');
const sortSelect = document.getElementById('sortProperties');
const favoriteButtons = document.querySelectorAll('.favorite-btn');
const paginationLinks = document.querySelectorAll('.page-link');

// Properties Data (you can replace this with data from your backend)
const propertiesData = [
    {
        id: 1,
        title: 'Modern Apartment',
        price: '$350,000',
        address: '123 Main St, New York, NY',
        description: 'Beautiful modern apartment in the heart of New York City with stunning views and high-end finishes.',
        image: 'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1770&q=80',
        beds: 2,
        baths: 2,
        sqft: 1200,
        type: 'sale'
    },
    {
        id: 2,
        title: 'Luxury Villa',
        price: '$1,200,000',
        address: '456 Ocean Ave, Miami, FL',
        description: 'Stunning luxury villa with ocean views, private pool, and spacious living areas perfect for entertaining.',
        image: 'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1775&q=80',
        beds: 5,
        baths: 4,
        sqft: 3500,
        type: 'sale'
    },
    {
        id: 3,
        title: 'Family Home',
        price: '$750,000',
        address: '789 Oak St, Chicago, IL',
        description: 'Spacious family home in a quiet neighborhood with a large backyard, updated kitchen, and finished basement.',
        image: 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1770&q=80',
        beds: 4,
        baths: 3,
        sqft: 2800,
        type: 'sale'
    },
    {
        id: 4,
        title: 'Penthouse Suite',
        price: '$950,000',
        address: '567 Park Ave, Los Angeles, CA',
        description: 'Luxurious penthouse suite with panoramic city views, gourmet kitchen, and private rooftop terrace.',
        image: 'https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1770&q=80',
        beds: 3,
        baths: 3.5,
        sqft: 2200,
        type: 'sale'
    },
    {
        id: 5,
        title: 'Downtown Loft',
        price: '$2,500/mo',
        address: '123 Broadway, Seattle, WA',
        description: 'Modern downtown loft with industrial finishes, open floor plan, and floor-to-ceiling windows.',
        image: 'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1753&q=80',
        beds: 1,
        baths: 1,
        sqft: 950,
        type: 'rent'
    },
    {
        id: 6,
        title: 'Suburban Townhouse',
        price: '$3,200/mo',
        address: '456 Maple Ave, Austin, TX',
        description: 'Spacious townhouse in a family-friendly neighborhood with community pool and playground.',
        image: 'https://images.unsplash.com/photo-1600573472550-8090b5e0745e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1770&q=80',
        beds: 3,
        baths: 2.5,
        sqft: 1800,
        type: 'rent'
    }
];

// Initialize properties
function initProperties() {
    // Check URL parameters for any filters
    const urlParams = new URLSearchParams(window.location.search);
    const typeParam = urlParams.get('type');
    const locationParam = urlParams.get('location');
    const priceParam = urlParams.get('price');
    
    // Set filter values if they exist in URL
    if (typeParam) {
        document.querySelector('select[name="property-type"]').value = typeParam;
    }
    
    if (locationParam) {
        document.querySelector('select[name="location"]').value = locationParam;
    }
    
    if (priceParam) {
        document.querySelector('select[name="price-range"]').value = priceParam;
    }
    
    // Render properties
    renderProperties(propertiesData);
}

// Render properties in grid view
function renderProperties(properties) {
    if (!propertiesGrid) return;
    
    propertiesGrid.innerHTML = '';
    
    properties.forEach(property => {
        const propertyCard = document.createElement('div');
        propertyCard.classList.add('property-card');
        propertyCard.setAttribute('data-id', property.id);
        
        propertyCard.innerHTML = `
            <div class="property-img">
                <img src="${property.image}" alt="${property.title}">
                <div class="property-tag ${property.type === 'rent' ? 'for-rent' : 'for-sale'}">${property.type === 'rent' ? 'For Rent' : 'For Sale'}</div>
                <button class="favorite-btn"><i class="far fa-heart"></i></button>
            </div>
            <div class="property-content">
                <div class="property-price">${property.price}</div>
                <h3 class="property-title">${property.title}</h3>
                <p class="property-address"><i class="fas fa-map-marker-alt"></i> ${property.address}</p>
                <div class="property-specs">
                    <span><i class="fas fa-bed"></i> ${property.beds} Beds</span>
                    <span><i class="fas fa-bath"></i> ${property.baths} Baths</span>
                    <span><i class="fas fa-vector-square"></i> ${property.sqft} sq ft</span>
                </div>
                <a href="property-details.html?id=${property.id}" class="btn view-details">View Details</a>
            </div>
        `;
        
        propertiesGrid.appendChild(propertyCard);
    });
    
    // Re-initialize favorite buttons
    initFavoriteButtons();
}

// Render properties in list view
function renderPropertiesList(properties) {
    if (!propertiesList) return;
    
    propertiesList.innerHTML = '';
    
    properties.forEach(property => {
        const propertyCard = document.createElement('div');
        propertyCard.classList.add('list-property-card');
        propertyCard.setAttribute('data-id', property.id);
        
        propertyCard.innerHTML = `
            <div class="list-property-img">
                <img src="${property.image}" alt="${property.title}">
                <div class="property-tag ${property.type === 'rent' ? 'for-rent' : 'for-sale'}">${property.type === 'rent' ? 'For Rent' : 'For Sale'}</div>
                <button class="favorite-btn"><i class="far fa-heart"></i></button>
            </div>
            <div class="list-property-content">
                <div class="list-property-header">
                    <div class="list-property-title">
                        <h3>${property.title}</h3>
                        <p class="list-property-address"><i class="fas fa-map-marker-alt"></i> ${property.address}</p>
                    </div>
                    <div class="list-property-price">${property.price}</div>
                </div>
                <p class="list-property-description">${property.description}</p>
                <div class="list-property-specs">
                    <span><i class="fas fa-bed"></i> ${property.beds} Beds</span>
                    <span><i class="fas fa-bath"></i> ${property.baths} Baths</span>
                    <span><i class="fas fa-vector-square"></i> ${property.sqft} sq ft</span>
                </div>
                <a href="property-details.html?id=${property.id}" class="btn list-view-details">View Details</a>
            </div>
        `;
        
        propertiesList.appendChild(propertyCard);
    });
    
    // Re-initialize favorite buttons
    initFavoriteButtons();
}

// Toggle view between grid and list
function toggleView() {
    gridViewBtn?.addEventListener('click', () => {
        gridViewBtn.classList.add('active');
        listViewBtn.classList.remove('active');
        propertiesGrid.style.display = 'grid';
        propertiesList.style.display = 'none';
    });
    
    listViewBtn?.addEventListener('click', () => {
        listViewBtn.classList.add('active');
        gridViewBtn.classList.remove('active');
        propertiesGrid.style.display = 'none';
        propertiesList.style.display = 'flex';
        
        // Render list view if it's empty
        if (propertiesList.children.length === 0) {
            renderPropertiesList(propertiesData);
        }
    });
}

// Toggle advanced filters
function toggleAdvancedFilters() {
    filterToggle?.addEventListener('click', () => {
        filterToggle.classList.toggle('active');
        advancedFilters.classList.toggle('active');
        
        if (advancedFilters.classList.contains('active')) {
            advancedFilters.style.display = 'grid';
            filterToggle.innerHTML = 'Hide Advanced Filters <i class="fas fa-chevron-up"></i>';
        } else {
            advancedFilters.style.display = 'none';
            filterToggle.innerHTML = 'Show Advanced Filters <i class="fas fa-chevron-down"></i>';
        }
    });
}

// Reset filters
function resetFilters() {
    resetFilterBtn?.addEventListener('click', () => {
        const filterInputs = document.querySelectorAll('.filter-group select, .filter-group input');
        filterInputs.forEach(input => {
            input.value = '';
        });
        
        // Reset checkboxes
        const checkboxes = document.querySelectorAll('.filter-group input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        
        // Render all properties
        renderProperties(propertiesData);
        if (propertiesList.style.display === 'flex') {
            renderPropertiesList(propertiesData);
        }
    });
}

// Apply filters
function applyFilters() {
    applyFilterBtn?.addEventListener('click', () => {
        const propertyType = document.querySelector('select[name="property-type"]')?.value;
        const location = document.querySelector('select[name="location"]')?.value;
        const priceRange = document.querySelector('select[name="price-range"]')?.value;
        const minBeds = document.querySelector('select[name="min-beds"]')?.value;
        const minBaths = document.querySelector('select