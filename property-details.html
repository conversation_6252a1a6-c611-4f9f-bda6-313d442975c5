<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Luxury Beachfront Villa - Property Details | Elite Real Estate</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/property-details.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/lightgallery@2.4.0/css/lightgallery.min.css">
</head>
<body>
    <header>
        <div class="container">
            <h1 class="logo">Elite Real Estate</h1>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="properties.html" class="active">Properties</a></li>
                    <li><a href="services.html">Services</a></li>
                    <li><a href="agents.html">Our Agents</a></li>
                    <li><a href="about.html">About Us</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
                <div class="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </nav>
        </div>
    </header>

    <section class="page-header">
        <div class="container">
            <h1>Luxury Beachfront Villa</h1>
            <div class="breadcrumb">
                <a href="index.html">Home</a> / <a href="properties.html">Properties</a> / <span>Luxury Beachfront Villa</span>
            </div>
        </div>
    </section>

    <section class="property-details">
        <div class="container">
            <div class="property-main">
                <div class="property-gallery">
                    <div class="main-image">
                        <img src="https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1775&q=80" alt="Luxury Villa">
                    </div>
                    <div class="gallery-thumbnails" id="lightgallery">
                        <a href="https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1775&q=80" class="thumbnail active">
                            <img src="https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1775&q=80" alt="Exterior">
                        </a>
                        <a href="https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1753&q=80" class="thumbnail">
                            <img src="https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1753&q=80" alt="Living Room">
                        </a>
                        <a href="https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1770&q=80" class="thumbnail">
                            <img src="https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1770&q=80" alt="Kitchen">
                        </a>
                        <a href="https://images.unsplash.com/photo-1600210492493-0946911123ea?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1974&q=80" class="thumbnail">
                            <img src="https://images.unsplash.com/photo-1600210492493-0946911123ea?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1974&q=80" alt="Bedroom">
                        </a>
                        <a href="https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1770&q=80" class="thumbnail">
                            <img src="https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1770&q=80" alt="Bathroom">
                        </a>
                    </div>
                </div>

                <div class="property-info-main">
                    <div class="property-header">
                        <div class="property-title">
                            <h2>Luxury Beachfront Villa</h2>
                            <p class="property-location"><i class="fas fa-map-marker-alt"></i> 456 Ocean Ave, Miami, FL</p>
                        </div>
                        <div class="property-price">
                            <h3>$1,200,000</h3>
                            <span class="property-id">Property ID: LV-2023-456</span>
                        </div>
                    </div>
                    
                    <div class="property-features">
                        <div class="feature">
                            <i class="fas fa-bed"></i>
                            <span>5 Bedrooms</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-bath"></i>
                            <span>4 Bathrooms</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-vector-square"></i>
                            <span>3,500 sq ft</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-car"></i>
                            <span>2 Garage Spaces</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-calendar-alt"></i>
                            <span>Built in 2018</span>
                        </div>
                    </div>
                    
                    <div class="property-actions">
                        <a href="#schedule-viewing" class="btn primary-btn"><i class="fas fa-calendar-check"></i> Schedule Viewing</a>
                        <button class="btn secondary-btn"><i class="far fa-heart"></i> Add to Favorites</button>
                        <button class="btn share-btn"><i class="fas fa-share-alt"></i> Share</button>
                    </div>
                </div>
            </div>
            
            <div class="property-details-tabs">
                <div class="tabs-nav">
                    <button class="tab-btn active" data-tab="description">Description</button>
                    <button class="tab-btn" data-tab="details">Details</button>
                    <button class="tab-btn" data-tab="features">Features</button>
                    <button class="tab-btn" data-tab="floor-plans">Floor Plans</button>
                    <button class="tab-btn" data-tab="video">Video</button>
                    <button class="tab-btn" data-tab="location">Location</button>
                </div>
                
                <div class="tabs-content">
                    <div class="tab-pane active" id="description">
                        <h3>Property Description</h3>
                        <p>Welcome to this stunning luxury beachfront villa located in the prestigious Ocean Avenue neighborhood of Miami. This magnificent property offers breathtaking ocean views and unparalleled luxury living.</p>
                        
                        <p>The villa features an open concept design with high ceilings and floor-to-ceiling windows that flood the space with natural light and showcase the spectacular ocean views. The main level includes a grand foyer, formal living room, gourmet kitchen with top-of-the-line appliances, formal dining area, and a spacious family room that opens to the outdoor living space.</p>
                        
                        <p>The upper level houses the luxurious primary suite with a private balcony, spa-like bathroom, and large walk-in closet. Four additional bedrooms, each with their own en-suite bathroom, provide ample space for family and guests.</p>
                        
                        <p>The outdoor space is an entertainer's dream with a resort-style pool, spa, outdoor kitchen, and multiple seating areas. Direct beach access makes this property truly special.</p>
                        
                        <p>Additional features include smart home technology, a home theater, wine cellar, fitness room, and a two-car garage. This exceptional property offers the perfect blend of luxury, comfort, and beachfront living.</p>
                    </div>
                    
                    <div class="tab-pane" id="details">
                        <h3>Property Details</h3>
                        <div class="details-grid">
                            <div class="details-column">
                                <div class="detail-item">
                                    <span class="detail-label">Property Type:</span>
                                    <span class="detail-value">Villa</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Property Status:</span>
                                    <span class="detail-value">For Sale</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Property ID:</span>
                                    <span class="detail-value">LV-2023-456</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Price:</span>
                                    <span class="detail-value">$1,200,000</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Bedrooms:</span>
                                    <span class="detail-value">5</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Bathrooms:</span>
                                    <span class="detail-value">4</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Garage:</span>
                                    <span class="detail-value">2 Cars</span>
                                </div>
                            </div>
                            <div class="details-column">
                                <div class="detail-item">
                                    <span class="detail-label">Property Size:</span>
                                    <span class="detail-value">3,500 sq ft</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Lot Size:</span>
                                    <span class="detail-value">0.5 acres</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Year Built:</span>
                                    <span class="detail-value">2018</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Property Style:</span>
                                    <span class="detail-value">Modern</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Floors:</span>
                                    <span class="detail-value">2</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Basement:</span>
                                    <span class="detail-value">Yes, Finished</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Heating:</span>
                                    <span class="detail-value">Central</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-pane" id="features">
                        <h3>Property Features</h3>
                        <div class="features-grid">
                            <div class="features-column">
                                <h4>Interior Features</h4>
                                <ul class="features-list">
                                    <li><i class="fas fa-check"></i> Gourmet Kitchen</li>
                                    <li><i class="fas fa-check"></i> Marble Countertops</li>
                                    <li><i class="fas fa-check"></i> High Ceilings</li>
                                    <li><i class="fas fa-check"></i> Hardwood Floors</li>
                                    <li><i class="fas fa-check"></i> Walk-in Closets</li>
                                    <li><i class="fas fa-check"></i> Home Theater</li>
                                    <li><i class="fas fa-check"></i> Wine Cellar</li>
                                    <li><i class="fas fa-check"></i> Fitness Room</li>
                                    <li><i class="fas fa-check"></i> Smart Home Technology</li>
                                </ul>
                            </div>
                            <div class="features-column">
                                <h4>Exterior Features</h4>
                                <ul class="features-list">
                                    <li><i class="fas fa-check"></i> Beachfront</li>
                                    <li><i class="fas fa-check"></i> Private Pool</li>
                                    <li><i class="fas fa-check"></i> Hot Tub/Spa</li>
                                    <li><i class="fas fa-check"></i> Outdoor Kitchen</li>
                                    <li><i class="fas fa-check"></i> Covered Patio</li>
                                    <li><i class="fas fa-check"></i> Landscaped Garden</li>
                                    <li><i class="fas fa-check"></i> Outdoor Lighting</li>
                                    <li><i class="fas fa-check"></i> Security System</li>
                                    <li><i class="fas fa-check"></i> Gated Community</li>
                                </ul>
                            </div>
                            <div class="features-column">
                                <h4>Community Features</h4>
                                <ul class="features-list">
                                    <li><i class="fas fa-check"></i> Beach Access</li>
                                    <li><i class="fas fa-check"></i> Community Pool</li>
                                    <li><i class="fas fa-check"></i> Tennis Courts</li>
                                    <li><i class="fas fa-check"></i> Clubhouse</li>
                                    <li><i class="fas fa-check"></i> 24/7 Security</li>
                                    <li><i class="fas fa-check"></i> Concierge Service</li>
                                    <li><i class="fas fa-check"></i> Fitness Center</li>
                                    <li><i class="fas fa-check"></i> Walking Trails</li>
                                    <li><i class="fas fa-check"></i> Marina Access</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-pane" id="floor-plans">
                        <h3>Floor Plans</h3>
                        <div class="floor-plans-container">
                            <div class="floor-plan">
                                <h4>First Floor</h4>
                                <img src="https://images.unsplash.com/photo-1580122468928-0e9940385cb1?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1770&q=80" alt="First Floor Plan">
                                <div class="floor-plan-details">
                                    <div class="floor-plan-info">
                                        <span>Size:</span>
                                        <span>1,800 sq ft</span>
                                    </div>
                                    <div class="floor-plan-info">
                                        <span>Rooms:</span>
                                        <span>Living Room, Kitchen, Dining Room, Family Room, Office, 1 Bedroom, 1.5 Bathrooms</span>
                                    </div>
                                </div>
                                <a href="#" class="btn secondary-btn">Download Floor Plan</a>
                            </div>
                            <div class="floor-plan">
                                <h4>Second Floor</h4>
                                <img src="https://images.unsplash.com/photo-1580122468928-0e9940385cb1?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1770&q=80" alt="Second Floor Plan">
                                <div class="floor-plan-details">
                                    <div class="floor-plan-info">
                                        <span>Size:</span>
                                        <span>1,700 sq ft</span>
                                    </div>
                                    <div class="floor-plan-info">
                                        <span>Rooms:</span>
                                        <span>Master Suite, 3 Bedrooms, 2.5 Bathrooms, Laundry Room</span>
                                    </div>
                                </div>
                                <a href="#" class="btn secondary-btn">Download Floor Plan</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-pane" id="video">
                        <h3>Property Video Tour</h3>
                        <div class="video-container">
                            <iframe width="100%" height="500" src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Property Video Tour" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                        </div>
                    </div>
                    
                    <div class="tab-pane" id="location">
                        <h3>Property Location</h3>
                        <div class="map-container">
                            <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d14354.455066398794!2d-80.13005674999999!3d25.790654!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x88d9b3c9c72a826f%3A0x5d5d5e5d5d5d5d5d!2sMiami%20Beach%2C%20FL!5e0!3m2!1sen!2sus!4v1623456789012!5m2!1sen!2sus" width="100%" height="450" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
                        </div>
                        <div class="location-info">
                            <h4>Nearby Amenities</h4>
                            <div class="amenities-grid">
                                <div class="amenity">
                                    <i class="fas fa-graduation-cap"></i>
                                    <h5>Education</h5>
                                    <ul>
                                        <li>Miami Beach Elementary (0.5 miles)</li>
                                        <li>Ocean View Middle School (1.2 miles)</li>
                                        <li>Beachside High School (2.3 miles)</li>
                                    </ul>
                                </div>
                                <div class="amenity">
                                    <i class="fas fa-shopping-cart"></i>
                                    <h5>Shopping</h5>
                                    <ul>
                                        <li>Ocean Mall (0.8 miles)</li>
                                        <li>Beachfront Plaza (1.5 miles)</li>
                                        <li>Miami Shopping Center (3.2 miles)</li>
                                    </ul>
                                </div>
                                <div class="amenity">
                                    <i class="fas fa-utensils"></i>
                                    <h5>Dining</h5>
                                    <ul>
                                        <li>Oceanview Restaurant (0.3 miles)</li>
                                        <li>Beach Cafe (0.7 miles)</li>
                                        <li>Sunset Grill (1.1 miles)</li>
                                    </ul>
                                </div>
                                <div class="amenity">
                                    <i class="fas fa-bus"></i>
                                    <h5>Transportation</h5>
                                    <ul>
                                        <li>Bus Stop (0.2 miles)</li>
                                        <li>Miami Beach Station (1.8 miles)</li>
                                        <li>Miami International Airport (12 miles)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="property-contact" id="schedule-viewing">
                <div class="agent-info">
                    <div class="agent-image">
                        <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=774&q=80" alt="Agent">
                    </div>
                    <div class="agent-details">
                        <h3>Michael Johnson</h3>
                        <p class="agent-title">Luxury Property Specialist</p>
                        <div class="agent-contact">
                            <p><i class="fas fa-phone"></i> (*************</p>
                            <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        </div>
                        <div class="agent-social">
                            <a href="#"><i class="fab fa-facebook-f"></i></a>
                            <a href="#"><i class="fab fa-twitter"></i></a>
                            <a href="#"><i class="fab fa-linkedin-in"></i></a>
                            <a href="#"><i class="fab fa-instagram"></i></a>
                        </div>
                        <a href="agents.html#michael-johnson" class="btn secondary-btn">View Profile</a>
                    </div>
                </div>
                
                <div class="contact-form">
                    <h3>Schedule a Viewing</h3>
                    <form id="viewingForm">
                        <div class="form-group">
                            <input type="text" placeholder="Your Name" required>
                        </div>
                        <div class="form-group">
                            <input type="email" placeholder="Your Email" required>
                        </div>
                        <div class="form-group">
                            <input type="tel" placeholder="Your Phone">
                        </div>
                        <div class="form-group">
                            <input type="date" placeholder="Preferred Date" required>
                        </div>
                        <div class="form-group">
                            <select required>
                                <option value="" disabled selected>Preferred Time</option>
                                <option value="morning">Morning (9AM - 12PM)</option>
                                <option value="afternoon">Afternoon (12PM - 4PM)</option>
                                <option value="evening">Evening (4PM - 7PM)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <textarea placeholder="Your Message" rows="4"></textarea>
                        </div>
                        <button type="submit" class="btn primary-btn">Submit Request</button>
                    </form>
                </div>
            </div>
            
            <div class="similar-properties">
                <h2 class="section-title">Similar Properties</h2>
                <div class="properties-slider">
                    <div class="property-card">
                        <div class="property-img">
                            <img src="https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1753&q=80" alt="Modern Apartment">
                            <div class="property-tag">For Sale</div>
                            <button class="favorite-btn"><i class="far fa-heart"></i></button>
                        </div>
                        <div class="property-content">
                            <div class="property-price">$350,000</div>
                            <h3 class="property-title">Modern Apartment</h3>
                            <p class="property-address"><i class="fas fa-map-marker-alt"></i> 123 Main St, New York, NY</p>
                            <div class="property-specs">
                                <span><i class="fas fa-bed"></i> 2 Beds</span>
                                <span><i class="fas fa-bath"></i> 2 Baths</span>
                                <span><i class="fas fa-vector-square"></i> 1,200 sq ft</span>
                            </div>
                            <a href="property-details.html" class="btn view-details">View Details</a>
                        </div>
                    </div>
                    <div class="property-card">
                        <div class="property-img">
                            <img src="https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1770&q=80" alt="Family Home">
                            <div class="property-tag">For Sale</div>
                            <button class="favorite-btn"><i class="far fa-heart"></i></button>
                        </div>
                        <div class="property-content">
                            <div class="property-price">$750,000</div>
                            <h3 class="property-title">Family Home</h3>
                            <p class="property-address"><i class="fas fa-map-marker-alt"></i> 789 Oak St, Chicago, IL</p>
                            <div class="property-specs">
                                <span><i class="fas fa-bed"></i> 4 Beds</span>
                                <span><i class="fas fa-bath"></i> 3 Baths</span>
                                <span><i class="fas fa-vector-square"></i> 2,800 sq ft</span>
                            </div>
                            <a href="property-details.html" class="btn view-details">View Details</a>
                        </div>
                    </div>
                    <div class="property-card">
                        <div class="property-img">
                            <img src="https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1770&q=80" alt="Penthouse Suite">
                            <div class="property-tag">For Sale</div>
                            <button class="favorite-btn"><i class="far fa-heart"></i></button>
                        </div>
                        <div class="property-content">
                            <div class="property-price">$950,000</div>
                            <h3 class="property-title">Penthouse Suite</h3>
                            <p class="property-address"><i class="fas fa-map-marker-alt"></i> 567 Park Ave, Los Angeles, CA</p>
                            <div class="property-specs">
                                <span><i class="fas fa-bed"></i> 3 Beds</span>
                                <span><i class="fas fa-bath"></i> 3.5 Baths</span>
                                <span><i class="fas fa-vector-square"></i> 2,200 sq ft</span>
                            </div>
                            <a href="property-details.html" class="btn view-details">View Details</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Ready to Find Your Dream Home?</h2>
                <p>Browse our extensive property listings or contact our agents for personalized assistance.</p>
                <div class="cta-buttons">
                    <a href="properties.html" class="btn primary-btn">Browse Properties</a>
                    <a href="contact.html" class="btn secondary-btn">Contact Us</a>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-about">
                    <h3>Elite Real Estate</h3>
                    <p>Your trusted partner in finding the perfect property. With years of experience and a dedicated team, we're committed to helping you achieve your real estate goals.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="properties.html">Properties</a></li>
                        <li><a href="services.html">Services</a></li>
                        <li><a href="agents.html">Our Agents</a></li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Contact Info</h3>
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <p>123 Real Estate Ave, City, Country</p>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <p>+****************</p>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <p><EMAIL></p>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-clock"></i>
                        <p>Mon-Fri: 9:00 AM - 6:00 PM</p>
                    </div>
                </div>
                <div class="footer-newsletter">
                    <h3>Newsletter</h3>
                    <p>Subscribe to our newsletter for the latest property listings and real estate news.</p>
                    <form id="newsletterForm">
                        <input type="email" placeholder="Your Email" required>
                        <button type="submit">Subscribe</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 Elite Real Estate. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/lightgallery@2.4.0/lightgallery.min.js"></script>
    <script src="js/script.js"></script>
    <script src="js/property-details.js"></script>
</body>
</html>
