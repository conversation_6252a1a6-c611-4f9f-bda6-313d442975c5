/* Property Details Page Styles */

.page-header {
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1773&q=80');
    background-size: cover;
    background-position: center;
    padding: 100px 0 50px;
    color: white;
    text-align: center;
}

.breadcrumb {
    margin-top: 10px;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: var(--secondary-color);
}

.breadcrumb span {
    opacity: 0.8;
}

.property-details {
    padding: 4rem 0;
    background-color: #f8f9fa;
}

.property-main {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.property-gallery {
    padding: 1rem;
}

.main-image {
    margin-bottom: 1rem;
    border-radius: 10px;
    overflow: hidden;
}

.main-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.gallery-thumbnails {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 0.5rem;
}

.thumbnail {
    border-radius: 5px;
    overflow: hidden;
    cursor: pointer;
    opacity: 0.7;
    transition: var(--transition);
}

.thumbnail img {
    width: 100%;
    height: 80px;
    object-fit: cover;
}

.thumbnail.active, .thumbnail:hover {
    opacity: 1;
}

.property-info-main {
    padding: 2rem;
}

.property-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.property-title h2 {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.property-location {
    color: var(--gray-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.property-price h3 {
    font-size: 1.8rem;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
    text-align: right;
}

.property-id {
    color: var(--gray-color);
    font-size: 0.9rem;
    display: block;
    text-align: right;
}

.property-features {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border
</
</augment_code_snippet>