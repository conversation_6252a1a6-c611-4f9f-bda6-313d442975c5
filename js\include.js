// Function to load header and footer components
function loadComponent(elementId, filePath) {
    fetch(filePath)
        .then(response => response.text())
        .then(data => {
            document.getElementById(elementId).innerHTML = data;
            
            // Set active navigation link based on current page
            setActiveNavLink();
        })
        .catch(error => {
            console.error('Error loading component:', error);
        });
}

// Function to set active navigation link
function setActiveNavLink() {
    const currentPage = window.location.pathname.split('/').pop() || 'index1.html';
    const navLinks = document.querySelectorAll('nav ul li a');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        const linkHref = link.getAttribute('href');
        
        if (linkHref === currentPage || 
            (currentPage === '' && linkHref === 'index1.html') ||
            (currentPage === 'index.html' && linkHref === 'index1.html')) {
            link.classList.add('active');
        }
    });
}

// Function to initialize page components
function initializePage() {
    // Load header if header-placeholder exists
    const headerPlaceholder = document.getElementById('header-placeholder');
    if (headerPlaceholder) {
        loadComponent('header-placeholder', 'components/header.html');
    }
    
    // Load footer if footer-placeholder exists
    const footerPlaceholder = document.getElementById('footer-placeholder');
    if (footerPlaceholder) {
        loadComponent('footer-placeholder', 'components/footer.html');
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializePage);

// Mobile navigation toggle
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for the header to load
    setTimeout(() => {
        const hamburger = document.querySelector('.hamburger');
        const navLinks = document.querySelector('nav ul');
        
        if (hamburger && navLinks) {
            hamburger.addEventListener('click', () => {
                navLinks.classList.toggle('active');
                hamburger.classList.toggle('active');
            });
        }
    }, 500);
});

// Newsletter form handler
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        const newsletterForm = document.getElementById('newsletterForm');
        if (newsletterForm) {
            newsletterForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const email = this.querySelector('input[type="email"]').value;
                alert('Thank you for subscribing! We will send updates to: ' + email);
                this.reset();
            });
        }

        // Contact form handler
        const contactForm = document.getElementById('contactForm');
        if (contactForm) {
            contactForm.addEventListener('submit', function(e) {
                e.preventDefault();
                alert('Thank you for your message! We will get back to you soon.');
                this.reset();
            });
        }

        // Registration form handler
        const registerForm = document.querySelector('.register-form');
        if (registerForm) {
            registerForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const password = this.querySelector('#password').value;
                const confirmPassword = this.querySelector('#confirm-password').value;

                if (password !== confirmPassword) {
                    alert('Passwords do not match!');
                    return;
                }

                alert('Registration successful! Welcome to Glory Real Estate.');
                this.reset();
            });
        }

        // Login form handler
        const loginForm = document.querySelector('.login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                alert('Login successful! Welcome back.');
                // Redirect to home page
                window.location.href = 'index1.html';
            });
        }

        // Viewing form handler
        const viewingForm = document.getElementById('viewingForm');
        if (viewingForm) {
            viewingForm.addEventListener('submit', function(e) {
                e.preventDefault();
                alert('Viewing request submitted! Our agent will contact you soon.');
                this.reset();
            });
        }
    }, 500);
});
