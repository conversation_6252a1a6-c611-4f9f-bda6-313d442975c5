input{
    color:black;
    /* background-color: blue; */
    border-radius: 10px;
    margin: 5px;
    width:45%;
    border:2px solid grey;
    padding:10px;
    font-weight: bold;
}
button{
    color:whitesmoke;
    background-color: rgb(147, 31, 31);
    padding:10px;
    margin:5px;
    width:45%;
    border-radius: 5px;
}
button:hover{
    color:blue;
    background-color: black;
}
form{
    padding:20px;
    background: linear(rgba(0 0 0 0.9) rgba(0 0 0 0.9));
    width:45%;
    box-shadow:0 0 20px rgb(114, 136, 95);
    border-radius: 8px;
    transition: 0.1s ease;
    transform:translateY(-10px);

}
label{
    color:rgb(19, 26, 228);
    font-weight: 60px;
}
h1{
    color:yellow;
    text-align: center;
    letter-spacing: 2px;
    text-shadow: 20px;
    font-family: Arial, Helvetica, sans-serif;
    font-weight: lighter;
    font-size:50px;
}
h1:hover{
    color:red;
}
body{
    background-image:url("picture/5.jpeg");
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}
.ero{ 
    text-align: right;
}
.eros{
    color:red;
    text-align: right;
}
.grey{
    color:red;
}
/* section{
    padding:10px;
} */
 a{
    text-decoration: none;
  float:left;
  padding:5px;

 }
 a:hover{
    color:red;
 }